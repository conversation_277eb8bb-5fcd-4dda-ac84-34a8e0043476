"use client";

import { useRef } from 'react';
import SectionTitleAnimation from './SectionTitleAnimation';

// Simple ServiceCard component that doesn't handle its own scroll logic
const ServiceCard = ({ title, description, features, visual }) => {
  const content = (
    <div className="space-y-4">
      <h3 className="font-heading font-bold text-2xl text-secondary">
        {title}
      </h3>
      <p className="text-md text-secondary leading-relaxed">
        {description}
      </p>
      <ul className="space-y-2 text-secondary">
        {features.map((feature, idx) => (
          <li key={idx} className="flex items-center">
            <span className="w-2 h-2 bg-secondary rounded-full mr-2"></span>
            {feature}
          </li>
        ))}
      </ul>
    </div>
  );

  const visualElement = (
    <div className="flex items-center justify-center bg-gray-100 rounded-lg p-8 min-h-[250px]">
      {visual}
    </div>
  );

  return (
    <div className="mx-auto mb-12 w-2/3">
      <div className="flex gap-8 items-center">
        {/* Text content - 1/3 of the container */}
        <div className="w-1/3">
          {content}
        </div>
        {/* Visual element - 2/3 of the container */}
        <div className="w-2/3">
          {visualElement}
        </div>
      </div>
    </div>
  );
};

// Multiple services for expanded layout
const services = [
  {
    title: "Brand Identity Design",
    description: "Create a cohesive visual identity that speaks to your audience. From logos to color palettes, I craft brand elements that tell your story and make you memorable in the marketplace.",
    features: [
      "Logo Design & Brand Guidelines",
      "Color Palette & Typography",
      "Business Card & Stationery Design"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-gray-300 rounded-full mx-auto mb-3 flex items-center justify-center">
          <span className="text-gray-500 text-sm">Brand Icon</span>
        </div>
        <p className="text-gray-600 text-sm">Visual for Brand Identity</p>
      </div>
    )
  },
  {
    title: "Web Development",
    description: "Build modern, responsive websites that perform beautifully across all devices. From concept to deployment, I create digital experiences that engage users and drive results.",
    features: [
      "Responsive Web Design",
      "Frontend & Backend Development",
      "Performance Optimization"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-blue-200 rounded-lg mx-auto mb-3 flex items-center justify-center">
          <span className="text-blue-600 text-sm">Web Icon</span>
        </div>
        <p className="text-gray-600 text-sm">Visual for Web Development</p>
      </div>
    )
  },
  {
    title: "UI/UX Design",
    description: "Design intuitive user interfaces and seamless user experiences. I focus on creating digital products that are not only beautiful but also functional and user-friendly.",
    features: [
      "User Interface Design",
      "User Experience Research",
      "Prototyping & Wireframing"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-green-200 rounded-lg mx-auto mb-3 flex items-center justify-center">
          <span className="text-green-600 text-sm">UX Icon</span>
        </div>
        <p className="text-gray-600 text-sm">Visual for UI/UX Design</p>
      </div>
    )
  },
  {
    title: "Digital Marketing",
    description: "Amplify your brand's reach with strategic digital marketing campaigns. From social media to content strategy, I help businesses connect with their target audience effectively.",
    features: [
      "Social Media Strategy",
      "Content Creation & Marketing",
      "SEO & Analytics"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-purple-200 rounded-lg mx-auto mb-3 flex items-center justify-center">
          <span className="text-purple-600 text-sm">Marketing Icon</span>
        </div>
        <p className="text-gray-600 text-sm">Visual for Digital Marketing</p>
      </div>
    )
  }
];

const Services = () => {
  const sectionRef = useRef(null);

  return (
    <>
      {/* Reusable Section Title Animation */}
      <SectionTitleAnimation
        title="Services"
        currentSectionRef={sectionRef}
        previousSectionSelector='[data-section="home"]'
        zIndex="z-0"
        className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
      />

      {/* Services Section - provides scroll space and natural service card layout */}
      <section
        ref={sectionRef}
        data-section="services"
        className="bg-primary py-16 min-h-[300vh] relative z-10"
      >
        {/* Services Content Container with background to cover title */}
        <div className="w-full h-full bg-primary">
          <div className="w-3/4 mx-auto px-6">
            {/* Spacer to push services down initially so they appear from bottom */}
            <div className="h-[100vh]"></div>

            {/* Service Cards - positioned naturally in the document flow */}
            {services.map((service, index) => (
              <ServiceCard
                key={index}
                title={service.title}
                description={service.description}
                features={service.features}
                visual={service.visual}
              />
            ))}

            {/* Bottom spacer to provide scroll area */}
            <div className="h-[100vh]"></div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Services;
