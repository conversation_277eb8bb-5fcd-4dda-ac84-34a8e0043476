"use client";

import { useRef, useState, useEffect } from 'react';
import AnimatedFixedTitle from './AnimatedFixedTitle';

// Simple ServiceCard component that doesn't handle its own scroll logic
const ServiceCard = ({ title, description, features, visual }) => {
  const content = (
    <div className="space-y-4">
      <h3 className="font-heading font-bold text-2xl text-secondary">
        {title}
      </h3>
      <p className="text-md text-secondary leading-relaxed">
        {description}
      </p>
      <ul className="space-y-2 text-secondary">
        {features.map((feature, idx) => (
          <li key={idx} className="flex items-center">
            <span className="w-2 h-2 bg-secondary rounded-full mr-2"></span>
            {feature}
          </li>
        ))}
      </ul>
    </div>
  );

  const visualElement = (
    <div className="flex items-center justify-center bg-gray-100 rounded-lg p-8 min-h-[250px]">
      {visual}
    </div>
  );

  return (
    <div className="mx-auto mb-12 w-2/3">
      <div className="flex gap-8 items-center">
        {/* Text content - 1/3 of the container */}
        <div className="w-1/3">
          {content}
        </div>
        {/* Visual element - 2/3 of the container */}
        <div className="w-2/3">
          {visualElement}
        </div>
      </div>
    </div>
  );
};

// Multiple services for expanded layout
const services = [
  {
    title: "Brand Identity Design",
    description: "Create a cohesive visual identity that speaks to your audience. From logos to color palettes, I craft brand elements that tell your story and make you memorable in the marketplace.",
    features: [
      "Logo Design & Brand Guidelines",
      "Color Palette & Typography",
      "Business Card & Stationery Design"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-gray-300 rounded-full mx-auto mb-3 flex items-center justify-center">
          <span className="text-gray-500 text-sm">Brand Icon</span>
        </div>
        <p className="text-gray-600 text-sm">Visual for Brand Identity</p>
      </div>
    )
  },
  {
    title: "Web Development",
    description: "Build modern, responsive websites that perform beautifully across all devices. From concept to deployment, I create digital experiences that engage users and drive results.",
    features: [
      "Responsive Web Design",
      "Frontend & Backend Development",
      "Performance Optimization"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-blue-200 rounded-lg mx-auto mb-3 flex items-center justify-center">
          <span className="text-blue-600 text-sm">Web Icon</span>
        </div>
        <p className="text-gray-600 text-sm">Visual for Web Development</p>
      </div>
    )
  },
  {
    title: "UI/UX Design",
    description: "Design intuitive user interfaces and seamless user experiences. I focus on creating digital products that are not only beautiful but also functional and user-friendly.",
    features: [
      "User Interface Design",
      "User Experience Research",
      "Prototyping & Wireframing"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-green-200 rounded-lg mx-auto mb-3 flex items-center justify-center">
          <span className="text-green-600 text-sm">UX Icon</span>
        </div>
        <p className="text-gray-600 text-sm">Visual for UI/UX Design</p>
      </div>
    )
  },
  {
    title: "Digital Marketing",
    description: "Amplify your brand's reach with strategic digital marketing campaigns. From social media to content strategy, I help businesses connect with their target audience effectively.",
    features: [
      "Social Media Strategy",
      "Content Creation & Marketing",
      "SEO & Analytics"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-purple-200 rounded-lg mx-auto mb-3 flex items-center justify-center">
          <span className="text-purple-600 text-sm">Marketing Icon</span>
        </div>
        <p className="text-gray-600 text-sm">Visual for Digital Marketing</p>
      </div>
    )
  }
];

const Services = () => {
  const sectionRef = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [titleVisible, setTitleVisible] = useState(false);
  const [scrollEffects, setScrollEffects] = useState({
    opacity: 1,
    blur: 0,
    scale: 1
  });

  useEffect(() => {
    const handleScroll = () => {
      if (!sectionRef.current) return;

      const servicesRect = sectionRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Services is the first section after Home, so trigger when Services section comes into view
      const servicesTop = servicesRect.top;

      // Show title when Services section is approaching the viewport
      const triggerPoint = windowHeight * 0.7; // Show title when Services is 70% down the viewport
      const shouldShowTitle = servicesTop <= triggerPoint;

      // Show animation when scrolling down and condition is met
      if (shouldShowTitle && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setTitleVisible(true);
      }

      // Hide animation when scrolling back up past the same trigger point
      if (!shouldShowTitle && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setTitleVisible(false);
      }

      // Calculate scroll-driven effects when title is visible
      if (titleVisible) {
        // Calculate how far we've scrolled into the Services section
        const servicesTop = servicesRect.top;
        const servicesHeight = servicesRect.height;

        // Start services animations when Services section reaches top of viewport
        const triggerOffset = windowHeight * 0.1;
        if (servicesTop <= triggerOffset) {
          // Find the first service card to detect when it reaches the title
          const firstServiceCard = sectionRef.current.querySelector('.mx-auto.mb-12.w-2\\/3');

          if (firstServiceCard) {
            const cardRect = firstServiceCard.getBoundingClientRect();
            const cardTop = cardRect.top;
            const screenCenter = windowHeight * 0.5; // Center of screen where title is

            // Calculate if the first card has reached the title area (with padding gap)
            const paddingGap = windowHeight * 0.15; // 15vh padding gap
            const cardReachedTitle = cardTop <= (screenCenter + paddingGap);

            if (cardReachedTitle) {
              // Cards caught up - start applying scroll-driven effects to title
              const fadeDistance = windowHeight * 0.3; // Effects over 30vh
              const cardPastTitle = Math.max(0, (screenCenter + paddingGap) - cardTop);
              const effectProgress = Math.min(1, cardPastTitle / fadeDistance);

              // Add a delay buffer - title stays crisp for the first 15% of effect progress
              const delayBuffer = 0.15;
              const adjustedProgress = Math.max(0, (effectProgress - delayBuffer) / (1 - delayBuffer));

              // Apply scroll-driven effects similar to Projects/Process sections
              const opacity = adjustedProgress > 0 ? Math.max(0.05, 1 - (adjustedProgress * 3)) : 1;
              const blur = adjustedProgress * 10;
              const scale = adjustedProgress > 0 ? Math.max(0.8, 1 - (adjustedProgress * 0.5)) : 1;

              setScrollEffects({ opacity, blur, scale });
            } else {
              // Cards haven't caught up yet - title fully visible with no effects
              setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
            }
          } else {
            // Fallback if card not found - title fully visible with no effects
            setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
          }
        } else {
          // Services section not in trigger zone - title fully visible with no effects
          setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
        }
      } else {
        // Title not visible yet - reset effects
        setScrollEffects({ opacity: 1, blur: 0, scale: 1 });
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn, titleVisible]);

  return (
    <>
      {/* Fixed/Centered Title with scroll-driven effects */}
      <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-20">
        <AnimatedFixedTitle
          title="Services"
          titleVisible={titleVisible}
          scrollEffects={scrollEffects}
          className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
          containerClassName=""
        />
      </div>

      {/* Services Section - provides scroll space and natural service card layout */}
      <section
        ref={sectionRef}
        data-section="services"
        className="bg-primary py-16 min-h-[300vh]"
      >
        <div className="w-3/4 mx-auto px-6">
          {/* Spacer to push services down initially so they appear from bottom */}
          <div className="h-[100vh]"></div>

          {/* Service Cards - positioned naturally in the document flow */}
          {services.map((service, index) => (
            <ServiceCard
              key={index}
              title={service.title}
              description={service.description}
              features={service.features}
              visual={service.visual}
            />
          ))}

          {/* Bottom spacer to provide scroll area */}
          <div className="h-[100vh]"></div>
        </div>
      </section>
    </>
  );
};

export default Services;
